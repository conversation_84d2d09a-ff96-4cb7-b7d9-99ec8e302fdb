<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Callback</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="/js/supabase-client.js"></script>
</head>
<body>
    <div class="container">
        <h1>Authenticating...</h1>
        <p>Please wait while we complete your sign-in process.</p>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                console.log('Auth callback: Starting authentication process...');
                
                // Get URL parameters
                const params = new URLSearchParams(window.location.search);
                const code = params.get('code');
                
                if (code) {
                    // Exchange the code for a session
                    const { data, error } = await supabase.auth.getSession();

                    if (error) {
                        console.error('Supabase session error:', error);
                        throw error;
                    }
                    
                    if (data.session) {
                        const { user, session } = data.session;
                        console.log('Supabase session obtained for user:', user.email);
                        console.log('Full user object:', user);
                        
                        // Send user data to backend
                        const response = await fetch('/api/auth/google', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ user, session }),
                            credentials: 'same-origin'
                        });
                        
                        const result = await response.json();
                        
                        if (result.status === 'success') {
                            console.log('Backend session created successfully');
                            
                            // Store user data in localStorage as backup
                            localStorage.setItem('userEmail', result.user.email);
                            localStorage.setItem('userName', result.user.full_name || result.user.email);
                            localStorage.setItem('userId', result.user.id);
                            localStorage.setItem('userLoggedIn', 'true');
                            localStorage.setItem('auth_timestamp', Date.now().toString());
                            
                            // Redirect to profile page
                            console.log('Redirecting to profile page...');
                            window.location.href = '/profile.html';
                        } else {
                            console.error('Backend authentication failed:', result.message);
                            throw new Error(result.message || 'Backend authentication failed');
                        }
                    } else {
                        console.error('No session found after code exchange');
                        console.log('Full data object:', data);
                        window.location.href = '/login.html?error=no_session';
                    }
                } else {
                    console.error('No authorization code found in URL');
                    window.location.href = '/login.html?error=no_code';
                }
            } catch (error) {
                console.error('Error in auth callback:', error);
                window.location.href = '/login.html?error=' + encodeURIComponent(error.message);
            }
        });
    </script>
</body>
</html>


